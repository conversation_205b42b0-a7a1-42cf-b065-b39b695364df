import 'package:dio/dio.dart';

import '../../../../core/network/dio_client.dart';
import '../models/categoria_model.dart';

class CategoriasRemoteDataSource {
  final DioClient dioClient;

  CategoriasRemoteDataSource({required this.dioClient});

  Future<List<CategoriaModel>> getCategorias({
    String? search,
    bool? ativo,
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) {
        queryParameters['search'] = search;
      }

      if (ativo != null) {
        queryParameters['ativo'] = ativo;
      }

      final response = await dioClient.get(
        '/categorias',
        queryParameters: queryParameters,
      );

      if (response.data['success'] == true) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar categorias');
      }
    } on DioException catch (e) {
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<CategoriaModel> getCategoria(int id) async {
    try {
      final response = await dioClient.get('/categorias/$id');

      if (response.data['success'] == true) {
        return CategoriaModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar categoria');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Categoria não encontrada');
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<CategoriaModel> createCategoria(CategoriaModel categoria) async {
    try {
      final response = await dioClient.post(
        '/categorias',
        data: categoria.toCreateJson(),
      );

      if (response.data['success'] == true) {
        return CategoriaModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao criar categoria');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response!.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final errorMessages = errors.values
              .expand((error) => error is List ? error : [error])
              .join(', ');
          throw Exception(errorMessages);
        }
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<CategoriaModel> updateCategoria(CategoriaModel categoria) async {
    try {
      final response = await dioClient.put(
        '/categorias/${categoria.id}',
        data: categoria.toCreateJson(),
      );

      if (response.data['success'] == true) {
        return CategoriaModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao atualizar categoria');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Categoria não encontrada');
      }
      if (e.response?.statusCode == 422) {
        final errors = e.response!.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final errorMessages = errors.values
              .expand((error) => error is List ? error : [error])
              .join(', ');
          throw Exception(errorMessages);
        }
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<void> deleteCategoria(int id) async {
    try {
      final response = await dioClient.delete('/categorias/$id');

      if (response.data['success'] != true) {
        throw Exception(response.data['message'] ?? 'Erro ao excluir categoria');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Categoria não encontrada');
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<List<CategoriaModel>> searchCategorias(String query) async {
    try {
      final response = await dioClient.get(
        '/categorias',
        queryParameters: {
          'search': query,
          'per_page': 10,
        },
      );

      if (response.data['success'] == true) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar categorias');
      }
    } on DioException catch (e) {
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }
}

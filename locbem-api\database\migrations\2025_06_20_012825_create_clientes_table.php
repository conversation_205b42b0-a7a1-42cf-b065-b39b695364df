<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clientes', function (Blueprint $table) {
            $table->id();
            $table->enum('tipo_pessoa', ['fisica', 'juridica']);
            $table->string('nome');
            $table->string('cpf_cnpj', 18)->unique();
            $table->string('rg_ie', 20)->nullable();
            $table->string('email')->nullable();
            $table->string('telefone', 20)->nullable();
            $table->string('celular', 20)->nullable();

            // Endereço de locação
            $table->string('endereco_locacao');
            $table->string('numero_locacao', 10);
            $table->string('complemento_locacao')->nullable();
            $table->string('bairro_locacao');
            $table->string('cidade_locacao');
            $table->string('estado_locacao', 2);
            $table->string('cep_locacao', 10);

            // Endereço de cobrança (opcional, se diferente)
            $table->boolean('endereco_cobranca_diferente')->default(false);
            $table->string('endereco_cobranca')->nullable();
            $table->string('numero_cobranca', 10)->nullable();
            $table->string('complemento_cobranca')->nullable();
            $table->string('bairro_cobranca')->nullable();
            $table->string('cidade_cobranca')->nullable();
            $table->string('estado_cobranca', 2)->nullable();
            $table->string('cep_cobranca', 10)->nullable();

            $table->text('observacoes')->nullable();
            $table->boolean('ativo')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clientes');
    }
};

import '../datasources/categorias_remote_datasource.dart';
import '../models/categoria_model.dart';

abstract class CategoriasRepository {
  Future<List<CategoriaModel>> getCategorias({
    String? search,
    bool? ativo,
    int page = 1,
    int perPage = 15,
  });
  Future<CategoriaModel> getCategoria(int id);
  Future<CategoriaModel> createCategoria(CategoriaModel categoria);
  Future<CategoriaModel> updateCategoria(CategoriaModel categoria);
  Future<void> deleteCategoria(int id);
  Future<List<CategoriaModel>> searchCategorias(String query);
}

class CategoriasRepositoryImpl implements CategoriasRepository {
  final CategoriasRemoteDataSource remoteDataSource;

  CategoriasRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<CategoriaModel>> getCategorias({
    String? search,
    bool? ativo,
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      return await remoteDataSource.getCategorias(
        search: search,
        ativo: ativo,
        page: page,
        perPage: perPage,
      );
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<CategoriaModel> getCategoria(int id) async {
    try {
      return await remoteDataSource.getCategoria(id);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<CategoriaModel> createCategoria(CategoriaModel categoria) async {
    try {
      return await remoteDataSource.createCategoria(categoria);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<CategoriaModel> updateCategoria(CategoriaModel categoria) async {
    try {
      return await remoteDataSource.updateCategoria(categoria);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<void> deleteCategoria(int id) async {
    try {
      await remoteDataSource.deleteCategoria(id);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<CategoriaModel>> searchCategorias(String query) async {
    try {
      return await remoteDataSource.searchCategorias(query);
    } catch (e) {
      throw Exception(e.toString());
    }
  }
}

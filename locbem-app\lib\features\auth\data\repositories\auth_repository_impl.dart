import '../datasources/auth_remote_datasource.dart';
import '../models/user_model.dart';

abstract class AuthRepository {
  Future<UserModel> login({required String email, required String password});
  Future<UserModel> getCurrentUser();
  Future<void> logout();
  Future<bool> isLoggedIn();
  Future<UserModel?> getCachedUser();
}

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;

  AuthRepositoryImpl({required this.remoteDataSource});

  @override
  Future<UserModel> login({required String email, required String password}) async {
    try {
      final data = await remoteDataSource.login(email: email, password: password);
      return UserModel.fromJson(data['user']);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      return await remoteDataSource.getCurrentUser();
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<void> logout() async {
    try {
      await remoteDataSource.logout();
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      return await remoteDataSource.isLoggedIn();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<UserModel?> getCachedUser() async {
    try {
      return await remoteDataSource.getCachedUser();
    } catch (e) {
      return null;
    }
  }
}

<?php

namespace App\Traits;

trait ApiResponse
{
    /**
     * Resposta de sucesso
     */
    public function successResponse($data = null, $message = 'Operação realizada com sucesso', $code = 200)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $code);
    }

    /**
     * Resposta de erro
     */
    public function errorResponse($message = 'Erro interno do servidor', $code = 500, $errors = null)
    {
        $response = [
            'success' => false,
            'message' => $message
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }

    /**
     * Resposta de validação
     */
    public function validationErrorResponse($errors, $message = 'Dados de validação inválidos')
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], 422);
    }

    /**
     * Resposta de não encontrado
     */
    public function notFoundResponse($message = 'Recurso não encontrado')
    {
        return response()->json([
            'success' => false,
            'message' => $message
        ], 404);
    }

    /**
     * Resposta de não autorizado
     */
    public function unauthorizedResponse($message = 'Não autorizado')
    {
        return response()->json([
            'success' => false,
            'message' => $message
        ], 401);
    }

    /**
     * Resposta de acesso negado
     */
    public function forbiddenResponse($message = 'Acesso negado')
    {
        return response()->json([
            'success' => false,
            'message' => $message
        ], 403);
    }

    /**
     * Resposta paginada
     */
    public function paginatedResponse($data, $message = 'Dados recuperados com sucesso')
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
            ]
        ]);
    }
}

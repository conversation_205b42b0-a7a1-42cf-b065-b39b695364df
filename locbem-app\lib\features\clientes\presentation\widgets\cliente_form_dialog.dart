import 'package:flutter/material.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../../../core/theme/app_theme.dart';
import '../../data/models/cliente_model.dart';

class ClienteFormDialog extends StatefulWidget {
  final ClienteModel? cliente;

  const ClienteFormDialog({super.key, this.cliente});

  @override
  State<ClienteFormDialog> createState() => _ClienteFormDialogState();
}

class _ClienteFormDialogState extends State<ClienteFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  int _currentPage = 0;

  // Controllers
  final _nomeController = TextEditingController();
  final _cpfCnpjController = TextEditingController();
  final _rgIeController = TextEditingController();
  final _emailController = TextEditingController();
  final _telefoneController = TextEditingController();
  final _celularController = TextEditingController();
  final _enderecoLocacaoController = TextEditingController();
  final _numeroLocacaoController = TextEditingController();
  final _complementoLocacaoController = TextEditingController();
  final _bairroLocacaoController = TextEditingController();
  final _cidadeLocacaoController = TextEditingController();
  final _estadoLocacaoController = TextEditingController();
  final _cepLocacaoController = TextEditingController();
  final _enderecoCobrancaController = TextEditingController();
  final _numeroCobrancaController = TextEditingController();
  final _complementoCobrancaController = TextEditingController();
  final _bairroCobrancaController = TextEditingController();
  final _cidadeCobrancaController = TextEditingController();
  final _estadoCobrancaController = TextEditingController();
  final _cepCobrancaController = TextEditingController();
  final _observacoesController = TextEditingController();

  // Form data
  String _tipoPessoa = 'fisica';
  bool _enderecoCobrancaDiferente = false;
  bool _ativo = true;

  // Masks
  final _cpfMask = MaskTextInputFormatter(mask: '###.###.###-##');
  final _cnpjMask = MaskTextInputFormatter(mask: '##.###.###/####-##');
  final _telefoneMask = MaskTextInputFormatter(mask: '(##) ####-####');
  final _celularMask = MaskTextInputFormatter(mask: '(##) #####-####');
  final _cepMask = MaskTextInputFormatter(mask: '#####-###');

  @override
  void initState() {
    super.initState();
    if (widget.cliente != null) {
      _loadClienteData();
    }
  }

  void _loadClienteData() {
    final cliente = widget.cliente!;
    _nomeController.text = cliente.nome;
    _cpfCnpjController.text = cliente.cpfCnpj;
    _rgIeController.text = cliente.rgIe ?? '';
    _emailController.text = cliente.email ?? '';
    _telefoneController.text = cliente.telefone ?? '';
    _celularController.text = cliente.celular ?? '';
    _enderecoLocacaoController.text = cliente.enderecoLocacao;
    _numeroLocacaoController.text = cliente.numeroLocacao;
    _complementoLocacaoController.text = cliente.complementoLocacao ?? '';
    _bairroLocacaoController.text = cliente.bairroLocacao;
    _cidadeLocacaoController.text = cliente.cidadeLocacao;
    _estadoLocacaoController.text = cliente.estadoLocacao;
    _cepLocacaoController.text = cliente.cepLocacao;
    _enderecoCobrancaController.text = cliente.enderecoCobranca ?? '';
    _numeroCobrancaController.text = cliente.numeroCobranca ?? '';
    _complementoCobrancaController.text = cliente.complementoCobranca ?? '';
    _bairroCobrancaController.text = cliente.bairroCobranca ?? '';
    _cidadeCobrancaController.text = cliente.cidadeCobranca ?? '';
    _estadoCobrancaController.text = cliente.estadoCobranca ?? '';
    _cepCobrancaController.text = cliente.cepCobranca ?? '';
    _observacoesController.text = cliente.observacoes ?? '';
    
    _tipoPessoa = cliente.tipoPessoa;
    _enderecoCobrancaDiferente = cliente.enderecoCobrancaDiferente;
    _ativo = cliente.ativo;
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _cpfCnpjController.dispose();
    _rgIeController.dispose();
    _emailController.dispose();
    _telefoneController.dispose();
    _celularController.dispose();
    _enderecoLocacaoController.dispose();
    _numeroLocacaoController.dispose();
    _complementoLocacaoController.dispose();
    _bairroLocacaoController.dispose();
    _cidadeLocacaoController.dispose();
    _estadoLocacaoController.dispose();
    _cepLocacaoController.dispose();
    _enderecoCobrancaController.dispose();
    _numeroCobrancaController.dispose();
    _complementoCobrancaController.dispose();
    _bairroCobrancaController.dispose();
    _cidadeCobrancaController.dispose();
    _estadoCobrancaController.dispose();
    _cepCobrancaController.dispose();
    _observacoesController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _saveCliente() {
    if (_formKey.currentState!.validate()) {
      final cliente = ClienteModel(
        id: widget.cliente?.id ?? 0,
        tipoPessoa: _tipoPessoa,
        nome: _nomeController.text.trim(),
        cpfCnpj: _cpfCnpjController.text.trim(),
        rgIe: _rgIeController.text.trim().isEmpty ? null : _rgIeController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        telefone: _telefoneController.text.trim().isEmpty ? null : _telefoneController.text.trim(),
        celular: _celularController.text.trim().isEmpty ? null : _celularController.text.trim(),
        enderecoLocacao: _enderecoLocacaoController.text.trim(),
        numeroLocacao: _numeroLocacaoController.text.trim(),
        complementoLocacao: _complementoLocacaoController.text.trim().isEmpty ? null : _complementoLocacaoController.text.trim(),
        bairroLocacao: _bairroLocacaoController.text.trim(),
        cidadeLocacao: _cidadeLocacaoController.text.trim(),
        estadoLocacao: _estadoLocacaoController.text.trim(),
        cepLocacao: _cepLocacaoController.text.trim(),
        enderecoCobrancaDiferente: _enderecoCobrancaDiferente,
        enderecoCobranca: _enderecoCobrancaDiferente && _enderecoCobrancaController.text.trim().isNotEmpty ? _enderecoCobrancaController.text.trim() : null,
        numeroCobranca: _enderecoCobrancaDiferente && _numeroCobrancaController.text.trim().isNotEmpty ? _numeroCobrancaController.text.trim() : null,
        complementoCobranca: _enderecoCobrancaDiferente && _complementoCobrancaController.text.trim().isNotEmpty ? _complementoCobrancaController.text.trim() : null,
        bairroCobranca: _enderecoCobrancaDiferente && _bairroCobrancaController.text.trim().isNotEmpty ? _bairroCobrancaController.text.trim() : null,
        cidadeCobranca: _enderecoCobrancaDiferente && _cidadeCobrancaController.text.trim().isNotEmpty ? _cidadeCobrancaController.text.trim() : null,
        estadoCobranca: _enderecoCobrancaDiferente && _estadoCobrancaController.text.trim().isNotEmpty ? _estadoCobrancaController.text.trim() : null,
        cepCobranca: _enderecoCobrancaDiferente && _cepCobrancaController.text.trim().isNotEmpty ? _cepCobrancaController.text.trim() : null,
        observacoes: _observacoesController.text.trim().isEmpty ? null : _observacoesController.text.trim(),
        ativo: _ativo,
        createdAt: widget.cliente?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      Navigator.of(context).pop(cliente);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        constraints: const BoxConstraints(
          maxWidth: 800,
          maxHeight: 700,
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildProgressIndicator(),
            Expanded(
              child: Form(
                key: _formKey,
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  children: [
                    _buildDadosGeraisPage(),
                    _buildEnderecoLocacaoPage(),
                    _buildEnderecoCobrancaPage(),
                  ],
                ),
              ),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppTheme.primaryBlue,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            widget.cliente == null ? Icons.person_add : Icons.person,
            color: Colors.white,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.cliente == null ? 'Novo Cliente' : 'Editar Cliente',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildStepIndicator(0, 'Dados Gerais'),
          _buildStepConnector(),
          _buildStepIndicator(1, 'Endereço de Locação'),
          _buildStepConnector(),
          _buildStepIndicator(2, 'Endereço de Cobrança'),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title) {
    final isActive = step == _currentPage;
    final isCompleted = step < _currentPage;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isActive || isCompleted ? AppTheme.primaryBlue : AppTheme.greyLight,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.circle,
              color: isActive || isCompleted ? Colors.white : AppTheme.greyMedium,
              size: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: isActive ? AppTheme.primaryBlue : AppTheme.greyMedium,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector() {
    return Container(
      height: 2,
      width: 32,
      color: AppTheme.greyLight,
      margin: const EdgeInsets.only(bottom: 20),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: AppTheme.greyLight),
        ),
      ),
      child: Row(
        children: [
          if (_currentPage > 0)
            OutlinedButton(
              onPressed: _previousPage,
              child: const Text('Anterior'),
            ),
          const Spacer(),
          if (_currentPage < 2)
            ElevatedButton(
              onPressed: _nextPage,
              child: const Text('Próximo'),
            )
          else
            ElevatedButton(
              onPressed: _saveCliente,
              child: Text(widget.cliente == null ? 'Criar' : 'Salvar'),
            ),
        ],
      ),
    );
  }

  Widget _buildDadosGeraisPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Dados Gerais',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Tipo de Pessoa
          const Text('Tipo de Pessoa'),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('Pessoa Física'),
                  value: 'fisica',
                  groupValue: _tipoPessoa,
                  onChanged: (value) {
                    setState(() {
                      _tipoPessoa = value!;
                      _cpfCnpjController.clear();
                      _rgIeController.clear();
                    });
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('Pessoa Jurídica'),
                  value: 'juridica',
                  groupValue: _tipoPessoa,
                  onChanged: (value) {
                    setState(() {
                      _tipoPessoa = value!;
                      _cpfCnpjController.clear();
                      _rgIeController.clear();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Nome
          TextFormField(
            controller: _nomeController,
            decoration: const InputDecoration(
              labelText: 'Nome *',
              hintText: 'Digite o nome completo',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Nome é obrigatório';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // CPF/CNPJ e RG/IE
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _cpfCnpjController,
                  inputFormatters: [_tipoPessoa == 'fisica' ? _cpfMask : _cnpjMask],
                  decoration: InputDecoration(
                    labelText: '${_tipoPessoa == 'fisica' ? 'CPF' : 'CNPJ'} *',
                    hintText: _tipoPessoa == 'fisica' ? '000.000.000-00' : '00.000.000/0000-00',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '${_tipoPessoa == 'fisica' ? 'CPF' : 'CNPJ'} é obrigatório';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _rgIeController,
                  decoration: InputDecoration(
                    labelText: _tipoPessoa == 'fisica' ? 'RG' : 'IE',
                    hintText: 'Digite o ${_tipoPessoa == 'fisica' ? 'RG' : 'IE'}',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Email
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: 'Email',
              hintText: 'Digite o email',
            ),
            validator: (value) {
              if (value != null && value.isNotEmpty && !value.contains('@')) {
                return 'Email inválido';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Telefone e Celular
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _telefoneController,
                  inputFormatters: [_telefoneMask],
                  decoration: const InputDecoration(
                    labelText: 'Telefone',
                    hintText: '(00) 0000-0000',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _celularController,
                  inputFormatters: [_celularMask],
                  decoration: const InputDecoration(
                    labelText: 'Celular',
                    hintText: '(00) 00000-0000',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Status
          SwitchListTile(
            title: const Text('Cliente Ativo'),
            value: _ativo,
            onChanged: (value) {
              setState(() {
                _ativo = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEnderecoLocacaoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Endereço de Locação',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // CEP
          TextFormField(
            controller: _cepLocacaoController,
            inputFormatters: [_cepMask],
            decoration: const InputDecoration(
              labelText: 'CEP *',
              hintText: '00000-000',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'CEP é obrigatório';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Endereço e Número
          Row(
            children: [
              Expanded(
                flex: 3,
                child: TextFormField(
                  controller: _enderecoLocacaoController,
                  decoration: const InputDecoration(
                    labelText: 'Endereço *',
                    hintText: 'Rua, Avenida, etc.',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Endereço é obrigatório';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _numeroLocacaoController,
                  decoration: const InputDecoration(
                    labelText: 'Número *',
                    hintText: '123',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Número é obrigatório';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Complemento
          TextFormField(
            controller: _complementoLocacaoController,
            decoration: const InputDecoration(
              labelText: 'Complemento',
              hintText: 'Apto, Bloco, etc.',
            ),
          ),
          const SizedBox(height: 16),

          // Bairro
          TextFormField(
            controller: _bairroLocacaoController,
            decoration: const InputDecoration(
              labelText: 'Bairro *',
              hintText: 'Digite o bairro',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Bairro é obrigatório';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Cidade e Estado
          Row(
            children: [
              Expanded(
                flex: 3,
                child: TextFormField(
                  controller: _cidadeLocacaoController,
                  decoration: const InputDecoration(
                    labelText: 'Cidade *',
                    hintText: 'Digite a cidade',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Cidade é obrigatória';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _estadoLocacaoController,
                  decoration: const InputDecoration(
                    labelText: 'Estado *',
                    hintText: 'SP',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Estado é obrigatório';
                    }
                    if (value.length != 2) {
                      return 'Use a sigla do estado';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnderecoCobrancaPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Endereço de Cobrança',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Checkbox para endereço diferente
          CheckboxListTile(
            title: const Text('Endereço de cobrança diferente do endereço de locação'),
            value: _enderecoCobrancaDiferente,
            onChanged: (value) {
              setState(() {
                _enderecoCobrancaDiferente = value ?? false;
                if (!_enderecoCobrancaDiferente) {
                  // Limpar campos de cobrança
                  _enderecoCobrancaController.clear();
                  _numeroCobrancaController.clear();
                  _complementoCobrancaController.clear();
                  _bairroCobrancaController.clear();
                  _cidadeCobrancaController.clear();
                  _estadoCobrancaController.clear();
                  _cepCobrancaController.clear();
                }
              });
            },
          ),
          const SizedBox(height: 16),

          if (_enderecoCobrancaDiferente) ...[
            // CEP
            TextFormField(
              controller: _cepCobrancaController,
              inputFormatters: [_cepMask],
              decoration: const InputDecoration(
                labelText: 'CEP *',
                hintText: '00000-000',
              ),
              validator: (value) {
                if (_enderecoCobrancaDiferente && (value == null || value.trim().isEmpty)) {
                  return 'CEP é obrigatório';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Endereço e Número
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextFormField(
                    controller: _enderecoCobrancaController,
                    decoration: const InputDecoration(
                      labelText: 'Endereço *',
                      hintText: 'Rua, Avenida, etc.',
                    ),
                    validator: (value) {
                      if (_enderecoCobrancaDiferente && (value == null || value.trim().isEmpty)) {
                        return 'Endereço é obrigatório';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _numeroCobrancaController,
                    decoration: const InputDecoration(
                      labelText: 'Número *',
                      hintText: '123',
                    ),
                    validator: (value) {
                      if (_enderecoCobrancaDiferente && (value == null || value.trim().isEmpty)) {
                        return 'Número é obrigatório';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Complemento
            TextFormField(
              controller: _complementoCobrancaController,
              decoration: const InputDecoration(
                labelText: 'Complemento',
                hintText: 'Apto, Bloco, etc.',
              ),
            ),
            const SizedBox(height: 16),

            // Bairro
            TextFormField(
              controller: _bairroCobrancaController,
              decoration: const InputDecoration(
                labelText: 'Bairro *',
                hintText: 'Digite o bairro',
              ),
              validator: (value) {
                if (_enderecoCobrancaDiferente && (value == null || value.trim().isEmpty)) {
                  return 'Bairro é obrigatório';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Cidade e Estado
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextFormField(
                    controller: _cidadeCobrancaController,
                    decoration: const InputDecoration(
                      labelText: 'Cidade *',
                      hintText: 'Digite a cidade',
                    ),
                    validator: (value) {
                      if (_enderecoCobrancaDiferente && (value == null || value.trim().isEmpty)) {
                        return 'Cidade é obrigatória';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _estadoCobrancaController,
                    decoration: const InputDecoration(
                      labelText: 'Estado *',
                      hintText: 'SP',
                    ),
                    validator: (value) {
                      if (_enderecoCobrancaDiferente && (value == null || value.trim().isEmpty)) {
                        return 'Estado é obrigatório';
                      }
                      if (_enderecoCobrancaDiferente && value != null && value.length != 2) {
                        return 'Use a sigla do estado';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.greyLight,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: AppTheme.greyMedium),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'O endereço de cobrança será o mesmo do endereço de locação.',
                      style: TextStyle(color: AppTheme.greyMedium),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Observações
          TextFormField(
            controller: _observacoesController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Observações',
              hintText: 'Informações adicionais sobre o cliente',
            ),
          ),
        ],
      ),
    );
  }
}

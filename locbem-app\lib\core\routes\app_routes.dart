import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/clientes/presentation/pages/clientes_page.dart';

class AppRoutes {
  static final GoRouter router = GoRouter(
    initialLocation: '/login',
    redirect: (context, state) {
      final authState = context.read<AuthBloc>().state;
      final isLoggedIn = authState is AuthAuthenticated;
      final isLoggingIn = state.matchedLocation == '/login';

      // Se não está logado e não está na tela de login, redirecionar para login
      if (!isLoggedIn && !isLoggingIn) {
        return '/login';
      }

      // Se está logado e está na tela de login, redirecionar para home
      if (isLoggedIn && isLoggingIn) {
        return '/home';
      }

      return null;
    },
    routes: [
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),
      GoRoute(
        path: '/clientes',
        name: 'clientes',
        builder: (context, state) => const ClientesPage(),
      ),
    ],
  );
}

// Classe para definir as rotas como constantes
class Routes {
  static const String login = '/login';
  static const String home = '/home';
  static const String clientes = '/clientes';
  static const String fornecedores = '/fornecedores';
  static const String categorias = '/categorias';
  static const String equipamentos = '/equipamentos';
  static const String contratos = '/contratos';
  static const String financeiro = '/financeiro';
  static const String relatorios = '/relatorios';
  static const String configuracoes = '/configuracoes';
}

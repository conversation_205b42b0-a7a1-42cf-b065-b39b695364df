<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cliente extends Model
{
    protected $fillable = [
        'tipo_pessoa',
        'nome',
        'cpf_cnpj',
        'rg_ie',
        'email',
        'telefone',
        'celular',
        'endereco_locacao',
        'numero_locacao',
        'complemento_locacao',
        'bairro_locacao',
        'cidade_locacao',
        'estado_locacao',
        'cep_locacao',
        'endereco_cobranca_diferente',
        'endereco_cobranca',
        'numero_cobranca',
        'complemento_cobranca',
        'bairro_cobranca',
        'cidade_cobranca',
        'estado_cobranca',
        'cep_cobranca',
        'observacoes',
        'ativo'
    ];

    protected $casts = [
        'endereco_cobranca_diferente' => 'boolean',
        'ativo' => 'boolean'
    ];
}

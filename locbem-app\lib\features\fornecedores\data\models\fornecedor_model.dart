class FornecedorModel {
  final int id;
  final String tipoPessoa;
  final String nome;
  final String cpfCnpj;
  final String? rgIe;
  final String? email;
  final String? telefone;
  final String? celular;
  final String endereco;
  final String numero;
  final String? complemento;
  final String bairro;
  final String cidade;
  final String estado;
  final String cep;
  final String? contato;
  final String? observacoes;
  final bool ativo;
  final DateTime createdAt;
  final DateTime updatedAt;

  FornecedorModel({
    required this.id,
    required this.tipoPessoa,
    required this.nome,
    required this.cpfCnpj,
    this.rgIe,
    this.email,
    this.telefone,
    this.celular,
    required this.endereco,
    required this.numero,
    this.complemento,
    required this.bairro,
    required this.cidade,
    required this.estado,
    required this.cep,
    this.contato,
    this.observacoes,
    required this.ativo,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FornecedorModel.fromJson(Map<String, dynamic> json) {
    return FornecedorModel(
      id: json['id'],
      tipoPessoa: json['tipo_pessoa'],
      nome: json['nome'],
      cpfCnpj: json['cpf_cnpj'],
      rgIe: json['rg_ie'],
      email: json['email'],
      telefone: json['telefone'],
      celular: json['celular'],
      endereco: json['endereco'],
      numero: json['numero'],
      complemento: json['complemento'],
      bairro: json['bairro'],
      cidade: json['cidade'],
      estado: json['estado'],
      cep: json['cep'],
      contato: json['contato'],
      observacoes: json['observacoes'],
      ativo: json['ativo'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tipo_pessoa': tipoPessoa,
      'nome': nome,
      'cpf_cnpj': cpfCnpj,
      'rg_ie': rgIe,
      'email': email,
      'telefone': telefone,
      'celular': celular,
      'endereco': endereco,
      'numero': numero,
      'complemento': complemento,
      'bairro': bairro,
      'cidade': cidade,
      'estado': estado,
      'cep': cep,
      'contato': contato,
      'observacoes': observacoes,
      'ativo': ativo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toCreateJson() {
    return {
      'tipo_pessoa': tipoPessoa,
      'nome': nome,
      'cpf_cnpj': cpfCnpj,
      'rg_ie': rgIe,
      'email': email,
      'telefone': telefone,
      'celular': celular,
      'endereco': endereco,
      'numero': numero,
      'complemento': complemento,
      'bairro': bairro,
      'cidade': cidade,
      'estado': estado,
      'cep': cep,
      'contato': contato,
      'observacoes': observacoes,
      'ativo': ativo,
    };
  }

  FornecedorModel copyWith({
    int? id,
    String? tipoPessoa,
    String? nome,
    String? cpfCnpj,
    String? rgIe,
    String? email,
    String? telefone,
    String? celular,
    String? endereco,
    String? numero,
    String? complemento,
    String? bairro,
    String? cidade,
    String? estado,
    String? cep,
    String? contato,
    String? observacoes,
    bool? ativo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FornecedorModel(
      id: id ?? this.id,
      tipoPessoa: tipoPessoa ?? this.tipoPessoa,
      nome: nome ?? this.nome,
      cpfCnpj: cpfCnpj ?? this.cpfCnpj,
      rgIe: rgIe ?? this.rgIe,
      email: email ?? this.email,
      telefone: telefone ?? this.telefone,
      celular: celular ?? this.celular,
      endereco: endereco ?? this.endereco,
      numero: numero ?? this.numero,
      complemento: complemento ?? this.complemento,
      bairro: bairro ?? this.bairro,
      cidade: cidade ?? this.cidade,
      estado: estado ?? this.estado,
      cep: cep ?? this.cep,
      contato: contato ?? this.contato,
      observacoes: observacoes ?? this.observacoes,
      ativo: ativo ?? this.ativo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get tipoDocumento => tipoPessoa == 'fisica' ? 'CPF' : 'CNPJ';
  String get tipoRegistro => tipoPessoa == 'fisica' ? 'RG' : 'IE';
  bool get isPessoaFisica => tipoPessoa == 'fisica';
  bool get isPessoaJuridica => tipoPessoa == 'juridica';

  String get enderecoCompleto {
    final complementoText = complemento?.isNotEmpty == true ? ', $complemento' : '';
    return '$endereco, $numero$complementoText - $bairro, $cidade/$estado - $cep';
  }

  String get contatoPrincipal {
    if (contato?.isNotEmpty == true) return contato!;
    if (email?.isNotEmpty == true) return email!;
    if (celular?.isNotEmpty == true) return celular!;
    if (telefone?.isNotEmpty == true) return telefone!;
    return 'Sem contato';
  }
}

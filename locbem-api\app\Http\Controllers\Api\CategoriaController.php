<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Categoria;
use App\Traits\ApiResponse;

class CategoriaController extends Controller
{
    use ApiResponse;

    public function index(Request $request)
    {
        $query = Categoria::query();

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('nome', 'like', "%{$search}%");
        }

        if ($request->has('ativo')) {
            $query->where('ativo', $request->boolean('ativo'));
        }

        $categorias = $query->orderBy('nome')->get();

        return $this->successResponse($categorias, 'Categorias recuperadas com sucesso');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nome' => 'required|string|max:255|unique:categorias',
            'descricao' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $categoria = Categoria::create($request->all());

        return $this->successResponse($categoria, 'Categoria criada com sucesso', 201);
    }

    public function show(string $id)
    {
        $categoria = Categoria::find($id);

        if (!$categoria) {
            return $this->notFoundResponse('Categoria não encontrada');
        }

        return $this->successResponse($categoria);
    }

    public function update(Request $request, string $id)
    {
        $categoria = Categoria::find($id);

        if (!$categoria) {
            return $this->notFoundResponse('Categoria não encontrada');
        }

        $validator = Validator::make($request->all(), [
            'nome' => 'sometimes|string|max:255|unique:categorias,nome,' . $id,
            'descricao' => 'nullable|string',
            'ativo' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $categoria->update($request->all());

        return $this->successResponse($categoria, 'Categoria atualizada com sucesso');
    }

    public function destroy(string $id)
    {
        $categoria = Categoria::find($id);

        if (!$categoria) {
            return $this->notFoundResponse('Categoria não encontrada');
        }

        $categoria->delete();

        return $this->successResponse(null, 'Categoria removida com sucesso');
    }
}

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../data/models/fornecedor_model.dart';
import '../../data/repositories/fornecedores_repository_impl.dart';

// Events
abstract class FornecedoresEvent extends Equatable {
  const FornecedoresEvent();

  @override
  List<Object?> get props => [];
}

class FornecedoresLoadEvent extends FornecedoresEvent {
  final String? search;
  final String? tipoPessoa;
  final bool? ativo;
  final bool refresh;

  const FornecedoresLoadEvent({
    this.search,
    this.tipoPessoa,
    this.ativo,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [search, tipoPessoa, ativo, refresh];
}

class FornecedorCreateEvent extends FornecedoresEvent {
  final FornecedorModel fornecedor;

  const FornecedorCreateEvent({required this.fornecedor});

  @override
  List<Object> get props => [fornecedor];
}

class FornecedorUpdateEvent extends FornecedoresEvent {
  final FornecedorModel fornecedor;

  const FornecedorUpdateEvent({required this.fornecedor});

  @override
  List<Object> get props => [fornecedor];
}

class FornecedorDeleteEvent extends FornecedoresEvent {
  final int fornecedorId;

  const FornecedorDeleteEvent({required this.fornecedorId});

  @override
  List<Object> get props => [fornecedorId];
}

class FornecedorSearchEvent extends FornecedoresEvent {
  final String query;

  const FornecedorSearchEvent({required this.query});

  @override
  List<Object> get props => [query];
}

// States
abstract class FornecedoresState extends Equatable {
  const FornecedoresState();

  @override
  List<Object?> get props => [];
}

class FornecedoresInitial extends FornecedoresState {}

class FornecedoresLoading extends FornecedoresState {}

class FornecedoresLoaded extends FornecedoresState {
  final List<FornecedorModel> fornecedores;
  final String? currentSearch;
  final String? currentTipoPessoa;
  final bool? currentAtivo;

  const FornecedoresLoaded({
    required this.fornecedores,
    this.currentSearch,
    this.currentTipoPessoa,
    this.currentAtivo,
  });

  @override
  List<Object?> get props => [fornecedores, currentSearch, currentTipoPessoa, currentAtivo];
}

class FornecedoresError extends FornecedoresState {
  final String message;

  const FornecedoresError({required this.message});

  @override
  List<Object> get props => [message];
}

class FornecedorOperationLoading extends FornecedoresState {}

class FornecedorOperationSuccess extends FornecedoresState {
  final String message;
  final FornecedorModel? fornecedor;

  const FornecedorOperationSuccess({
    required this.message,
    this.fornecedor,
  });

  @override
  List<Object?> get props => [message, fornecedor];
}

class FornecedorOperationError extends FornecedoresState {
  final String message;

  const FornecedorOperationError({required this.message});

  @override
  List<Object> get props => [message];
}

// BLoC
class FornecedoresBloc extends Bloc<FornecedoresEvent, FornecedoresState> {
  final FornecedoresRepository fornecedoresRepository;

  FornecedoresBloc({required this.fornecedoresRepository}) : super(FornecedoresInitial()) {
    on<FornecedoresLoadEvent>(_onLoadFornecedores);
    on<FornecedorCreateEvent>(_onCreateFornecedor);
    on<FornecedorUpdateEvent>(_onUpdateFornecedor);
    on<FornecedorDeleteEvent>(_onDeleteFornecedor);
    on<FornecedorSearchEvent>(_onSearchFornecedores);
  }

  Future<void> _onLoadFornecedores(FornecedoresLoadEvent event, Emitter<FornecedoresState> emit) async {
    if (!event.refresh) {
      emit(FornecedoresLoading());
    }

    try {
      final fornecedores = await fornecedoresRepository.getFornecedores(
        search: event.search,
        tipoPessoa: event.tipoPessoa,
        ativo: event.ativo,
      );

      emit(FornecedoresLoaded(
        fornecedores: fornecedores,
        currentSearch: event.search,
        currentTipoPessoa: event.tipoPessoa,
        currentAtivo: event.ativo,
      ));
    } catch (e) {
      emit(FornecedoresError(message: e.toString().replaceAll('Exception: ', '')));
    }
  }

  Future<void> _onCreateFornecedor(FornecedorCreateEvent event, Emitter<FornecedoresState> emit) async {
    emit(FornecedorOperationLoading());

    try {
      final fornecedor = await fornecedoresRepository.createFornecedor(event.fornecedor);
      emit(FornecedorOperationSuccess(
        message: 'Fornecedor criado com sucesso',
        fornecedor: fornecedor,
      ));
      
      // Recarregar lista
      add(const FornecedoresLoadEvent(refresh: true));
    } catch (e) {
      emit(FornecedorOperationError(message: e.toString().replaceAll('Exception: ', '')));
    }
  }

  Future<void> _onUpdateFornecedor(FornecedorUpdateEvent event, Emitter<FornecedoresState> emit) async {
    emit(FornecedorOperationLoading());

    try {
      final fornecedor = await fornecedoresRepository.updateFornecedor(event.fornecedor);
      emit(FornecedorOperationSuccess(
        message: 'Fornecedor atualizado com sucesso',
        fornecedor: fornecedor,
      ));
      
      // Recarregar lista
      add(const FornecedoresLoadEvent(refresh: true));
    } catch (e) {
      emit(FornecedorOperationError(message: e.toString().replaceAll('Exception: ', '')));
    }
  }

  Future<void> _onDeleteFornecedor(FornecedorDeleteEvent event, Emitter<FornecedoresState> emit) async {
    emit(FornecedorOperationLoading());

    try {
      await fornecedoresRepository.deleteFornecedor(event.fornecedorId);
      emit(const FornecedorOperationSuccess(message: 'Fornecedor excluído com sucesso'));
      
      // Recarregar lista
      add(const FornecedoresLoadEvent(refresh: true));
    } catch (e) {
      emit(FornecedorOperationError(message: e.toString().replaceAll('Exception: ', '')));
    }
  }

  Future<void> _onSearchFornecedores(FornecedorSearchEvent event, Emitter<FornecedoresState> emit) async {
    try {
      final fornecedores = await fornecedoresRepository.searchFornecedores(event.query);
      emit(FornecedoresLoaded(
        fornecedores: fornecedores,
        currentSearch: event.query,
      ));
    } catch (e) {
      emit(FornecedoresError(message: e.toString().replaceAll('Exception: ', '')));
    }
  }
}

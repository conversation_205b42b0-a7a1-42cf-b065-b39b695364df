import '../datasources/fornecedores_remote_datasource.dart';
import '../models/fornecedor_model.dart';

abstract class FornecedoresRepository {
  Future<List<FornecedorModel>> getFornecedores({
    String? search,
    String? tipoPessoa,
    bool? ativo,
    int page = 1,
    int perPage = 15,
  });
  Future<FornecedorModel> getFornecedor(int id);
  Future<FornecedorModel> createFornecedor(FornecedorModel fornecedor);
  Future<FornecedorModel> updateFornecedor(FornecedorModel fornecedor);
  Future<void> deleteFornecedor(int id);
  Future<List<FornecedorModel>> searchFornecedores(String query);
}

class FornecedoresRepositoryImpl implements FornecedoresRepository {
  final FornecedoresRemoteDataSource remoteDataSource;

  FornecedoresRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<FornecedorModel>> getFornecedores({
    String? search,
    String? tipoPessoa,
    bool? ativo,
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      return await remoteDataSource.getFornecedores(
        search: search,
        tipoPessoa: tipoPessoa,
        ativo: ativo,
        page: page,
        perPage: perPage,
      );
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<FornecedorModel> getFornecedor(int id) async {
    try {
      return await remoteDataSource.getFornecedor(id);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<FornecedorModel> createFornecedor(FornecedorModel fornecedor) async {
    try {
      return await remoteDataSource.createFornecedor(fornecedor);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<FornecedorModel> updateFornecedor(FornecedorModel fornecedor) async {
    try {
      return await remoteDataSource.updateFornecedor(fornecedor);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<void> deleteFornecedor(int id) async {
    try {
      await remoteDataSource.deleteFornecedor(id);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<FornecedorModel>> searchFornecedores(String query) async {
    try {
      return await remoteDataSource.searchFornecedores(query);
    } catch (e) {
      throw Exception(e.toString());
    }
  }
}

import '../../categorias/data/models/categoria_model.dart';
import '../../fornecedores/data/models/fornecedor_model.dart';

class EquipamentoModel {
  final int id;
  final String nome;
  final String? descricao;
  final String? marca;
  final String? modelo;
  final String? numeroSerie;
  final String? anoFabricacao;
  final double valorCompra;
  final double valorLocacaoDiaria;
  final String status;
  final int categoriaId;
  final int fornecedorId;
  final String? observacoes;
  final bool ativo;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Relacionamentos
  final CategoriaModel? categoria;
  final FornecedorModel? fornecedor;

  EquipamentoModel({
    required this.id,
    required this.nome,
    this.descricao,
    this.marca,
    this.modelo,
    this.numeroSerie,
    this.anoFabricacao,
    required this.valorCompra,
    required this.valorLocacaoDiaria,
    required this.status,
    required this.categoriaId,
    required this.fornecedorId,
    this.observacoes,
    required this.ativo,
    required this.createdAt,
    required this.updatedAt,
    this.categoria,
    this.fornecedor,
  });

  factory EquipamentoModel.fromJson(Map<String, dynamic> json) {
    return EquipamentoModel(
      id: json['id'],
      nome: json['nome'],
      descricao: json['descricao'],
      marca: json['marca'],
      modelo: json['modelo'],
      numeroSerie: json['numero_serie'],
      anoFabricacao: json['ano_fabricacao'],
      valorCompra: double.parse(json['valor_compra'].toString()),
      valorLocacaoDiaria: double.parse(json['valor_locacao_diaria'].toString()),
      status: json['status'],
      categoriaId: json['categoria_id'],
      fornecedorId: json['fornecedor_id'],
      observacoes: json['observacoes'],
      ativo: json['ativo'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      categoria: json['categoria'] != null 
          ? CategoriaModel.fromJson(json['categoria'])
          : null,
      fornecedor: json['fornecedor'] != null 
          ? FornecedorModel.fromJson(json['fornecedor'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'descricao': descricao,
      'marca': marca,
      'modelo': modelo,
      'numero_serie': numeroSerie,
      'ano_fabricacao': anoFabricacao,
      'valor_compra': valorCompra,
      'valor_locacao_diaria': valorLocacaoDiaria,
      'status': status,
      'categoria_id': categoriaId,
      'fornecedor_id': fornecedorId,
      'observacoes': observacoes,
      'ativo': ativo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toCreateJson() {
    return {
      'nome': nome,
      'descricao': descricao,
      'marca': marca,
      'modelo': modelo,
      'numero_serie': numeroSerie,
      'ano_fabricacao': anoFabricacao,
      'valor_compra': valorCompra,
      'valor_locacao_diaria': valorLocacaoDiaria,
      'status': status,
      'categoria_id': categoriaId,
      'fornecedor_id': fornecedorId,
      'observacoes': observacoes,
      'ativo': ativo,
    };
  }

  EquipamentoModel copyWith({
    int? id,
    String? nome,
    String? descricao,
    String? marca,
    String? modelo,
    String? numeroSerie,
    String? anoFabricacao,
    double? valorCompra,
    double? valorLocacaoDiaria,
    String? status,
    int? categoriaId,
    int? fornecedorId,
    String? observacoes,
    bool? ativo,
    DateTime? createdAt,
    DateTime? updatedAt,
    CategoriaModel? categoria,
    FornecedorModel? fornecedor,
  }) {
    return EquipamentoModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      marca: marca ?? this.marca,
      modelo: modelo ?? this.modelo,
      numeroSerie: numeroSerie ?? this.numeroSerie,
      anoFabricacao: anoFabricacao ?? this.anoFabricacao,
      valorCompra: valorCompra ?? this.valorCompra,
      valorLocacaoDiaria: valorLocacaoDiaria ?? this.valorLocacaoDiaria,
      status: status ?? this.status,
      categoriaId: categoriaId ?? this.categoriaId,
      fornecedorId: fornecedorId ?? this.fornecedorId,
      observacoes: observacoes ?? this.observacoes,
      ativo: ativo ?? this.ativo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      categoria: categoria ?? this.categoria,
      fornecedor: fornecedor ?? this.fornecedor,
    );
  }

  String get statusDisplay {
    switch (status) {
      case 'disponivel':
        return 'Disponível';
      case 'locado':
        return 'Locado';
      case 'manutencao':
        return 'Manutenção';
      case 'inativo':
        return 'Inativo';
      default:
        return status;
    }
  }

  bool get isDisponivel => status == 'disponivel';
  bool get isLocado => status == 'locado';
  bool get isManutencao => status == 'manutencao';
  bool get isInativo => status == 'inativo';

  String get nomeCompleto {
    final parts = <String>[nome];
    if (marca?.isNotEmpty == true) parts.add(marca!);
    if (modelo?.isNotEmpty == true) parts.add(modelo!);
    return parts.join(' - ');
  }

  String get identificacao {
    if (numeroSerie?.isNotEmpty == true) {
      return 'S/N: $numeroSerie';
    }
    return 'ID: $id';
  }

  @override
  String toString() {
    return 'EquipamentoModel(id: $id, nome: $nome, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EquipamentoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

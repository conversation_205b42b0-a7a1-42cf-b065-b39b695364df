<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrador',
                'password' => Hash::make('admin123'),
                'cpf' => '000.000.000-00',
                'telefone' => '(00) 00000-0000',
                'cargo' => 'Administrador do Sistema',
                'perfil' => 'admin',
                'permissoes' => json_encode(['all']),
                'ativo' => true,
            ]
        );
    }
}

import 'package:flutter/material.dart';

import '../theme/app_theme.dart';

class ResponsiveTable<T> extends StatelessWidget {
  final List<T> items;
  final List<TableColumn<T>> columns;
  final void Function(T item)? onRowTap;
  final Widget Function(T item)? mobileCardBuilder;
  final bool isLoading;
  final String? emptyMessage;
  final Widget? emptyWidget;

  const ResponsiveTable({
    super.key,
    required this.items,
    required this.columns,
    this.onRowTap,
    this.mobileCardBuilder,
    this.isLoading = false,
    this.emptyMessage,
    this.emptyWidget,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 800;

    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (items.isEmpty) {
      return _buildEmptyState(context);
    }

    if (isMobile && mobileCardBuilder != null) {
      return _buildMobileView();
    }

    return _buildDesktopTable();
  }

  Widget _buildEmptyState(BuildContext context) {
    if (emptyWidget != null) {
      return emptyWidget!;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inbox,
            size: 64,
            color: AppTheme.greyMedium,
          ),
          const SizedBox(height: 16),
          Text(
            emptyMessage ?? 'Nenhum item encontrado',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.greyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileView() {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: InkWell(
            onTap: onRowTap != null ? () => onRowTap!(item) : null,
            borderRadius: BorderRadius.circular(12),
            child: mobileCardBuilder!(item),
          ),
        );
      },
    );
  }

  Widget _buildDesktopTable() {
    return Card(
      margin: EdgeInsets.zero,
      child: Column(
        children: [
          // Header
          Container(
            decoration: const BoxDecoration(
              color: AppTheme.greyLight,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: columns.map((column) {
                return Expanded(
                  flex: column.flex,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      column.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.greyDark,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Rows
          Expanded(
            child: ListView.builder(
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isEven = index % 2 == 0;
                
                return InkWell(
                  onTap: onRowTap != null ? () => onRowTap!(item) : null,
                  child: Container(
                    decoration: BoxDecoration(
                      color: isEven ? Colors.white : AppTheme.greyLight.withOpacity(0.3),
                    ),
                    child: Row(
                      children: columns.map((column) {
                        return Expanded(
                          flex: column.flex,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: column.builder(item),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class TableColumn<T> {
  final String title;
  final Widget Function(T item) builder;
  final int flex;

  const TableColumn({
    required this.title,
    required this.builder,
    this.flex = 1,
  });
}

class UserModel {
  final int id;
  final String name;
  final String email;
  final String? cpf;
  final String? telefone;
  final String? cargo;
  final double? salario;
  final String? foto;
  final String perfil;
  final List<String> permissoes;
  final bool ativo;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.cpf,
    this.telefone,
    this.cargo,
    this.salario,
    this.foto,
    required this.perfil,
    required this.permissoes,
    required this.ativo,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      cpf: json['cpf'],
      telefone: json['telefone'],
      cargo: json['cargo'],
      salario: json['salario']?.toDouble(),
      foto: json['foto'],
      perfil: json['perfil'],
      permissoes: List<String>.from(json['permissoes'] ?? []),
      ativo: json['ativo'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'cpf': cpf,
      'telefone': telefone,
      'cargo': cargo,
      'salario': salario,
      'foto': foto,
      'perfil': perfil,
      'permissoes': permissoes,
      'ativo': ativo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? cpf,
    String? telefone,
    String? cargo,
    double? salario,
    String? foto,
    String? perfil,
    List<String>? permissoes,
    bool? ativo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      cpf: cpf ?? this.cpf,
      telefone: telefone ?? this.telefone,
      cargo: cargo ?? this.cargo,
      salario: salario ?? this.salario,
      foto: foto ?? this.foto,
      perfil: perfil ?? this.perfil,
      permissoes: permissoes ?? this.permissoes,
      ativo: ativo ?? this.ativo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool hasPermission(String permission) {
    return permissoes.contains('all') || permissoes.contains(permission);
  }

  bool get isAdmin => perfil == 'admin';
  bool get isGerente => perfil == 'gerente' || isAdmin;
  bool get isFuncionario => perfil == 'funcionario' || isGerente;
}

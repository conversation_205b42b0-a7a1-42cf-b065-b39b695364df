import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';

class ClienteFiltersDialog extends StatefulWidget {
  final String? currentTipoPessoa;
  final bool? currentAtivo;

  const ClienteFiltersDialog({
    super.key,
    this.currentTipoPessoa,
    this.currentAtivo,
  });

  @override
  State<ClienteFiltersDialog> createState() => _ClienteFiltersDialogState();
}

class _ClienteFiltersDialogState extends State<ClienteFiltersDialog> {
  String? _tipoPessoa;
  bool? _ativo;

  @override
  void initState() {
    super.initState();
    _tipoPessoa = widget.currentTipoPessoa;
    _ativo = widget.currentAtivo;
  }

  void _clearFilters() {
    setState(() {
      _tipoPessoa = null;
      _ativo = null;
    });
  }

  void _applyFilters() {
    Navigator.of(context).pop({
      'tipoPessoa': _tipoPessoa,
      'ativo': _ativo,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.filter_list,
                  color: AppTheme.primaryBlue,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Filtros',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Tipo de Pessoa
            const Text(
              'Tipo de Pessoa',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Column(
              children: [
                RadioListTile<String?>(
                  title: const Text('Todos'),
                  value: null,
                  groupValue: _tipoPessoa,
                  onChanged: (value) {
                    setState(() {
                      _tipoPessoa = value;
                    });
                  },
                ),
                RadioListTile<String?>(
                  title: const Text('Pessoa Física'),
                  value: 'fisica',
                  groupValue: _tipoPessoa,
                  onChanged: (value) {
                    setState(() {
                      _tipoPessoa = value;
                    });
                  },
                ),
                RadioListTile<String?>(
                  title: const Text('Pessoa Jurídica'),
                  value: 'juridica',
                  groupValue: _tipoPessoa,
                  onChanged: (value) {
                    setState(() {
                      _tipoPessoa = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Status
            const Text(
              'Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Column(
              children: [
                RadioListTile<bool?>(
                  title: const Text('Todos'),
                  value: null,
                  groupValue: _ativo,
                  onChanged: (value) {
                    setState(() {
                      _ativo = value;
                    });
                  },
                ),
                RadioListTile<bool?>(
                  title: const Text('Ativo'),
                  value: true,
                  groupValue: _ativo,
                  onChanged: (value) {
                    setState(() {
                      _ativo = value;
                    });
                  },
                ),
                RadioListTile<bool?>(
                  title: const Text('Inativo'),
                  value: false,
                  groupValue: _ativo,
                  onChanged: (value) {
                    setState(() {
                      _ativo = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _clearFilters,
                    child: const Text('Limpar'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: const Text('Aplicar'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

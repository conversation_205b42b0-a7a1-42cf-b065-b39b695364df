<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contratos', function (Blueprint $table) {
            $table->id();
            $table->string('numero_contrato')->unique();
            $table->foreignId('cliente_id')->constrained('clientes');
            $table->foreignId('user_id')->constrained('users'); // funcionário responsável
            $table->date('data_inicio');
            $table->date('data_fim');
            $table->integer('dias_locacao');
            $table->decimal('valor_total', 10, 2);
            $table->decimal('valor_desconto', 10, 2)->default(0);
            $table->decimal('valor_final', 10, 2);
            $table->enum('status', ['em_locacao', 'em_retirada', 'finalizado', 'cancelado'])->default('em_locacao');
            $table->boolean('pago_na_hora')->default(false);
            $table->text('observacoes')->nullable();
            $table->timestamp('data_retirada')->nullable();
            $table->timestamp('data_devolucao')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contratos');
    }
};

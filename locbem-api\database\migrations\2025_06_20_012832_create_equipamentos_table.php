<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipamentos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('categoria_id')->constrained('categorias');
            $table->foreignId('fornecedor_id')->nullable()->constrained('fornecedores');
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->enum('tipo', ['unico', 'estoque']); // único = patrimônio, estoque = quantidade
            $table->string('numero_patrimonio')->nullable(); // para tipo único
            $table->integer('quantidade_estoque')->default(0); // para tipo estoque
            $table->integer('quantidade_disponivel')->default(0); // para tipo estoque
            $table->string('marca')->nullable();
            $table->string('modelo')->nullable();
            $table->year('ano')->nullable();
            $table->text('especificacoes')->nullable();
            $table->integer('dias_manutencao')->default(0); // dias acumulados de uso
            $table->integer('limite_dias_manutencao')->default(365); // limite para manutenção
            $table->json('valores_periodo'); // JSON com valores por período de locação
            $table->string('foto')->nullable();
            $table->boolean('ativo')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipamentos');
    }
};

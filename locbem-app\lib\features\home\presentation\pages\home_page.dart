import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('LocBem - Dashboard'),
        actions: [
          BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state is AuthAuthenticated) {
                return PopupMenuButton(
                  icon: CircleAvatar(
                    backgroundColor: AppTheme.secondaryOrange,
                    child: Text(
                      state.user.name.isNotEmpty ? state.user.name[0].toUpperCase() : 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  itemBuilder: (context) => <PopupMenuEntry>[
                    PopupMenuItem(
                      child: ListTile(
                        leading: const Icon(Icons.person),
                        title: Text(state.user.name),
                        subtitle: Text(state.user.email),
                      ),
                    ),
                    const PopupMenuDivider(),
                    PopupMenuItem(
                      onTap: () {
                        context.read<AuthBloc>().add(AuthLogoutEvent());
                      },
                      child: const ListTile(
                        leading: Icon(Icons.logout),
                        title: Text('Sair'),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Boas-vindas
            BlocBuilder<AuthBloc, AuthState>(
              builder: (context, state) {
                if (state is AuthAuthenticated) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: AppTheme.primaryBlue,
                            child: Text(
                              state.user.name.isNotEmpty ? state.user.name[0].toUpperCase() : 'U',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Bem-vindo, ${state.user.name}!',
                                  style: Theme.of(context).textTheme.headlineSmall,
                                ),
                                Text(
                                  'Perfil: ${state.user.perfil.toUpperCase()}',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppTheme.greyMedium,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            const SizedBox(height: 24),
            
            // Menu de navegação
            Text(
              'Menu Principal',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: GridView.count(
                crossAxisCount: MediaQuery.of(context).size.width > 800 ? 4 : 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _MenuCard(
                    icon: Icons.people,
                    title: 'Clientes',
                    subtitle: 'Gerenciar clientes',
                    color: AppTheme.primaryBlue,
                    onTap: () => context.go('/clientes'),
                  ),
                  _MenuCard(
                    icon: Icons.business,
                    title: 'Fornecedores',
                    subtitle: 'Gerenciar fornecedores',
                    color: AppTheme.secondaryOrange,
                    onTap: () {
                      // TODO: Implementar navegação
                    },
                  ),
                  _MenuCard(
                    icon: Icons.build,
                    title: 'Equipamentos',
                    subtitle: 'Gerenciar equipamentos',
                    color: AppTheme.successGreen,
                    onTap: () {
                      // TODO: Implementar navegação
                    },
                  ),
                  _MenuCard(
                    icon: Icons.description,
                    title: 'Contratos',
                    subtitle: 'Gerenciar locações',
                    color: AppTheme.infoBlue,
                    onTap: () {
                      // TODO: Implementar navegação
                    },
                  ),
                  _MenuCard(
                    icon: Icons.attach_money,
                    title: 'Financeiro',
                    subtitle: 'Contas a pagar/receber',
                    color: AppTheme.warningAmber,
                    onTap: () {
                      // TODO: Implementar navegação
                    },
                  ),
                  _MenuCard(
                    icon: Icons.assessment,
                    title: 'Relatórios',
                    subtitle: 'Relatórios e análises',
                    color: AppTheme.greyDark,
                    onTap: () {
                      // TODO: Implementar navegação
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _MenuCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _MenuCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.greyMedium,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

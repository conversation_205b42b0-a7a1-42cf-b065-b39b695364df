<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PeriodoLocacaoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $periodos = [
            ['nome' => '1 dia', 'dias' => 1, 'customizado' => false],
            ['nome' => '3 dias', 'dias' => 3, 'customizado' => false],
            ['nome' => '1 semana', 'dias' => 7, 'customizado' => false],
            ['nome' => '15 dias', 'dias' => 15, 'customizado' => false],
            ['nome' => '1 mês', 'dias' => 30, 'customizado' => false],
        ];

        foreach ($periodos as $periodo) {
            \App\Models\PeriodoLocacao::create($periodo);
        }
    }
}

import 'package:dio/dio.dart';
import 'package:hive/hive.dart';

import '../../../../core/network/dio_client.dart';
import '../models/user_model.dart';

class AuthRemoteDataSource {
  final DioClient dioClient;

  AuthRemoteDataSource({required this.dioClient});

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await dioClient.post('/login', data: {
        'email': email,
        'password': password,
      });

      if (response.data['success'] == true) {
        final data = response.data['data'];
        
        // Salvar token no Hive
        final authBox = Hive.box('auth');
        await authBox.put('token', data['token']);
        await authBox.put('user', data['user']);
        
        return data;
      } else {
        throw Exception(response.data['message'] ?? 'Erro no login');
      }
    } on DioException catch (e) {
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<UserModel> getCurrentUser() async {
    try {
      final response = await dioClient.get('/me');

      if (response.data['success'] == true) {
        return UserModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar usuário');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Token inválido');
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<void> logout() async {
    try {
      await dioClient.post('/logout');
    } catch (e) {
      // Ignorar erros de logout, pois vamos limpar o token local mesmo assim
    } finally {
      // Limpar dados locais
      final authBox = Hive.box('auth');
      await authBox.clear();
    }
  }

  Future<String?> refreshToken() async {
    try {
      final response = await dioClient.post('/refresh');

      if (response.data['success'] == true) {
        final newToken = response.data['data']['token'];
        
        // Salvar novo token
        final authBox = Hive.box('auth');
        await authBox.put('token', newToken);
        
        return newToken;
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao renovar token');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        // Token expirado, fazer logout
        await logout();
        throw Exception('Sessão expirada');
      }
      throw Exception('Erro ao renovar token');
    }
  }

  Future<bool> isLoggedIn() async {
    final authBox = Hive.box('auth');
    final token = authBox.get('token');
    return token != null;
  }

  Future<UserModel?> getCachedUser() async {
    final authBox = Hive.box('auth');
    final userData = authBox.get('user');
    
    if (userData != null) {
      return UserModel.fromJson(Map<String, dynamic>.from(userData));
    }
    
    return null;
  }
}

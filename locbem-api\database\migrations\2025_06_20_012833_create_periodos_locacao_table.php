<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('periodos_locacao', function (Blueprint $table) {
            $table->id();
            $table->string('nome'); // Ex: "1 dia", "3 dias", "1 semana"
            $table->integer('dias');
            $table->boolean('customizado')->default(false);
            $table->boolean('ativo')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('periodos_locacao');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contas', function (Blueprint $table) {
            $table->id();
            $table->enum('tipo', ['receber', 'pagar']);
            $table->string('descricao');
            $table->decimal('valor_original', 10, 2);
            $table->decimal('valor_juros', 10, 2)->default(0);
            $table->decimal('valor_desconto', 10, 2)->default(0);
            $table->decimal('valor_final', 10, 2);
            $table->date('data_vencimento');
            $table->date('data_pagamento')->nullable();
            $table->enum('status', ['pendente', 'pago', 'vencido', 'cancelado'])->default('pendente');
            $table->foreignId('cliente_id')->nullable()->constrained('clientes');
            $table->foreignId('fornecedor_id')->nullable()->constrained('fornecedores');
            $table->foreignId('contrato_id')->nullable()->constrained('contratos');
            $table->integer('parcela_numero')->default(1);
            $table->integer('total_parcelas')->default(1);
            $table->string('forma_pagamento')->nullable();
            $table->text('observacoes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contas');
    }
};

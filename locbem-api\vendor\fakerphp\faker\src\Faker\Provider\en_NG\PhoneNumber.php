<?php

namespace Faker\Provider\en_NG;

class PhoneNumber extends \Faker\Provider\PhoneNumber
{
    protected static $formats = [
        // Local
        '0703#######',
        '0704#######',
        '0705#######',
        '0706#######',
        '0707#######',
        '0708#######',
        '0709#######',
        '0802#######',
        '0803#######',
        '0804#######',
        '0805#######',
        '0806#######',
        '0807#######',
        '0808#######',
        '0809#######',
        '0810#######',
        '0811#######',
        '0812#######',
        '0813#######',
        '0814#######',
        '0815#######',
        '0816#######',
        '0817#######',
        '0818#######',
        '0819#######',
        '0902#######',
        '0903#######',
        '0905#######',
        '0908#######',
        '0909#######',

        '+234703#######',
        '+234704#######',
        '+234705#######',
        '+234706#######',
        '+234707#######',
        '+234708#######',
        '+234709#######',
        '+234802#######',
        '+234803#######',
        '+234804#######',
        '+234805#######',
        '+234806#######',
        '+234807#######',
        '+234808#######',
        '+234809#######',
        '+234810#######',
        '+234811#######',
        '+234812#######',
        '+234813#######',
        '+234814#######',
        '+234815#######',
        '+234816#######',
        '+234817#######',
        '+234818#######',
        '+234819#######',
        '+234902#######',
        '+234903#######',
        '+234905#######',
        '+234908#######',
        '+234909#######',

        '0703 ### ####',
        '0704 ### ####',
        '0705 ### ####',
        '0706 ### ####',
        '0707 ### ####',
        '0708 ### ####',
        '0709 ### ####',
        '0802 ### ####',
        '0803 ### ####',
        '0804 ### ####',
        '0805 ### ####',
        '0806 ### ####',
        '0807 ### ####',
        '0808 ### ####',
        '0809 ### ####',
        '0810 ### ####',
        '0811 ### ####',
        '0812 ### ####',
        '0813 ### ####',
        '0814 ### ####',
        '0815 ### ####',
        '0816 ### ####',
        '0817 ### ####',
        '0818 ### ####',
        '0819 ### ####',
        '0902 ### ####',
        '0903 ### ####',
        '0905 ### ####',
        '0908 ### ####',
        '0909 ### ####',

        '+234 703 ### ####',
        '+234 704 ### ####',
        '+234 705 ### ####',
        '+234 706 ### ####',
        '+234 707 ### ####',
        '+234 708 ### ####',
        '+234 709 ### ####',
        '+234 802 ### ####',
        '+234 803 ### ####',
        '+234 804 ### ####',
        '+234 805 ### ####',
        '+234 806 ### ####',
        '+234 807 ### ####',
        '+234 808 ### ####',
        '+234 809 ### ####',
        '+234 810 ### ####',
        '+234 811 ### ####',
        '+234 812 ### ####',
        '+234 813 ### ####',
        '+234 814 ### ####',
        '+234 815 ### ####',
        '+234 816 ### ####',
        '+234 817 ### ####',
        '+234 818 ### ####',
        '+234 819 ### ####',
        '+234 902 ### ####',
        '+234 903 ### ####',
        '+234 905 ### ####',
        '+234 908 ### ####',
        '+234 909 ### ####',
    ];
}

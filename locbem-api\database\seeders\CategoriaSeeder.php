<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategoriaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categorias = [
            ['nome' => 'Ferramentas Elétricas', 'descricao' => 'Furadeiras, parafusadeiras, serras elétricas'],
            ['nome' => 'Equipamentos de Construção', 'descricao' => 'Betoneiras, andaimes, equipamentos pesados'],
            ['nome' => 'Ferramentas Manuais', 'descricao' => 'Martelos, chaves, alicates'],
            ['nome' => 'Equipamentos de Jardinagem', 'descricao' => 'Cortadores de grama, motosserras'],
            ['nome' => 'Equipamentos de Limpeza', 'descricao' => 'Aspiradores industriais, lavadoras de alta pressão'],
            ['nome' => 'Equipamentos de Segurança', 'descricao' => 'EPIs, equipamentos de proteção'],
        ];

        foreach ($categorias as $categoria) {
            \App\Models\Categoria::create($categoria);
        }
    }
}

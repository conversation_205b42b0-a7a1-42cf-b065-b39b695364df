import 'package:dio/dio.dart';

import '../../../../core/network/dio_client.dart';
import '../models/fornecedor_model.dart';

class FornecedoresRemoteDataSource {
  final DioClient dioClient;

  FornecedoresRemoteDataSource({required this.dioClient});

  Future<List<FornecedorModel>> getFornecedores({
    String? search,
    String? tipoPessoa,
    bool? ativo,
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) {
        queryParameters['search'] = search;
      }

      if (tipoPessoa != null && tipoPessoa.isNotEmpty) {
        queryParameters['tipo_pessoa'] = tipoPessoa;
      }

      if (ativo != null) {
        queryParameters['ativo'] = ativo;
      }

      final response = await dioClient.get(
        '/fornecedores',
        queryParameters: queryParameters,
      );

      if (response.data['success'] == true) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => FornecedorModel.fromJson(json)).toList();
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar fornecedores');
      }
    } on DioException catch (e) {
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<FornecedorModel> getFornecedor(int id) async {
    try {
      final response = await dioClient.get('/fornecedores/$id');

      if (response.data['success'] == true) {
        return FornecedorModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar fornecedor');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Fornecedor não encontrado');
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<FornecedorModel> createFornecedor(FornecedorModel fornecedor) async {
    try {
      final response = await dioClient.post(
        '/fornecedores',
        data: fornecedor.toCreateJson(),
      );

      if (response.data['success'] == true) {
        return FornecedorModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao criar fornecedor');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response!.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final errorMessages = errors.values
              .expand((error) => error is List ? error : [error])
              .join(', ');
          throw Exception(errorMessages);
        }
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<FornecedorModel> updateFornecedor(FornecedorModel fornecedor) async {
    try {
      final response = await dioClient.put(
        '/fornecedores/${fornecedor.id}',
        data: fornecedor.toCreateJson(),
      );

      if (response.data['success'] == true) {
        return FornecedorModel.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao atualizar fornecedor');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Fornecedor não encontrado');
      }
      if (e.response?.statusCode == 422) {
        final errors = e.response!.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final errorMessages = errors.values
              .expand((error) => error is List ? error : [error])
              .join(', ');
          throw Exception(errorMessages);
        }
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<void> deleteFornecedor(int id) async {
    try {
      final response = await dioClient.delete('/fornecedores/$id');

      if (response.data['success'] != true) {
        throw Exception(response.data['message'] ?? 'Erro ao excluir fornecedor');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Fornecedor não encontrado');
      }
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }

  Future<List<FornecedorModel>> searchFornecedores(String query) async {
    try {
      final response = await dioClient.get(
        '/fornecedores',
        queryParameters: {
          'search': query,
          'per_page': 10,
        },
      );

      if (response.data['success'] == true) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => FornecedorModel.fromJson(json)).toList();
      } else {
        throw Exception(response.data['message'] ?? 'Erro ao buscar fornecedores');
      }
    } on DioException catch (e) {
      if (e.response?.data != null) {
        throw Exception(e.response!.data['message'] ?? 'Erro de conexão');
      }
      throw Exception('Erro de conexão');
    }
  }
}

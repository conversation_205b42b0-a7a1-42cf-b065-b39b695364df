<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Cliente;
use App\Traits\ApiResponse;

class ClienteController extends Controller
{
    use ApiResponse;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Cliente::query();

        // Filtros
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nome', 'like', "%{$search}%")
                  ->orWhere('cpf_cnpj', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->has('tipo_pessoa')) {
            $query->where('tipo_pessoa', $request->tipo_pessoa);
        }

        if ($request->has('ativo')) {
            $query->where('ativo', $request->boolean('ativo'));
        }

        // Paginação
        $perPage = $request->get('per_page', 15);
        $clientes = $query->orderBy('nome')->paginate($perPage);

        return $this->paginatedResponse($clientes, 'Clientes recuperados com sucesso');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tipo_pessoa' => 'required|in:fisica,juridica',
            'nome' => 'required|string|max:255',
            'cpf_cnpj' => 'required|string|max:18|unique:clientes',
            'rg_ie' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'telefone' => 'nullable|string|max:20',
            'celular' => 'nullable|string|max:20',
            'endereco_locacao' => 'required|string|max:255',
            'numero_locacao' => 'required|string|max:10',
            'complemento_locacao' => 'nullable|string|max:255',
            'bairro_locacao' => 'required|string|max:255',
            'cidade_locacao' => 'required|string|max:255',
            'estado_locacao' => 'required|string|size:2',
            'cep_locacao' => 'required|string|max:10',
            'endereco_cobranca_diferente' => 'boolean',
            'endereco_cobranca' => 'nullable|string|max:255',
            'numero_cobranca' => 'nullable|string|max:10',
            'complemento_cobranca' => 'nullable|string|max:255',
            'bairro_cobranca' => 'nullable|string|max:255',
            'cidade_cobranca' => 'nullable|string|max:255',
            'estado_cobranca' => 'nullable|string|size:2',
            'cep_cobranca' => 'nullable|string|max:10',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $cliente = Cliente::create($request->all());

        return $this->successResponse($cliente, 'Cliente criado com sucesso', 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $cliente = Cliente::find($id);

        if (!$cliente) {
            return $this->notFoundResponse('Cliente não encontrado');
        }

        return $this->successResponse($cliente);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $cliente = Cliente::find($id);

        if (!$cliente) {
            return $this->notFoundResponse('Cliente não encontrado');
        }

        $validator = Validator::make($request->all(), [
            'tipo_pessoa' => 'sometimes|in:fisica,juridica',
            'nome' => 'sometimes|string|max:255',
            'cpf_cnpj' => 'sometimes|string|max:18|unique:clientes,cpf_cnpj,' . $id,
            'rg_ie' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'telefone' => 'nullable|string|max:20',
            'celular' => 'nullable|string|max:20',
            'endereco_locacao' => 'sometimes|string|max:255',
            'numero_locacao' => 'sometimes|string|max:10',
            'complemento_locacao' => 'nullable|string|max:255',
            'bairro_locacao' => 'sometimes|string|max:255',
            'cidade_locacao' => 'sometimes|string|max:255',
            'estado_locacao' => 'sometimes|string|size:2',
            'cep_locacao' => 'sometimes|string|max:10',
            'endereco_cobranca_diferente' => 'boolean',
            'endereco_cobranca' => 'nullable|string|max:255',
            'numero_cobranca' => 'nullable|string|max:10',
            'complemento_cobranca' => 'nullable|string|max:255',
            'bairro_cobranca' => 'nullable|string|max:255',
            'cidade_cobranca' => 'nullable|string|max:255',
            'estado_cobranca' => 'nullable|string|size:2',
            'cep_cobranca' => 'nullable|string|max:10',
            'observacoes' => 'nullable|string',
            'ativo' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $cliente->update($request->all());

        return $this->successResponse($cliente, 'Cliente atualizado com sucesso');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $cliente = Cliente::find($id);

        if (!$cliente) {
            return $this->notFoundResponse('Cliente não encontrado');
        }

        $cliente->delete();

        return $this->successResponse(null, 'Cliente removido com sucesso');
    }
}

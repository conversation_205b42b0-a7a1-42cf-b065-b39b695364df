{"name": "fakerphp/faker", "type": "library", "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["faker", "fixtures", "data"], "license": "MIT", "authors": [{"name": "<PERSON>"}], "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "require-dev": {"ext-intl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "autoload-dev": {"psr-4": {"Faker\\Test\\": "test/Faker/", "Faker\\Test\\Fixture\\": "test/Fixture/"}}, "conflict": {"fzaninotto/faker": "*"}, "suggest": {"ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality.", "doctrine/orm": "Required to use Faker\\ORM\\Doctrine"}, "config": {"allow-plugins": {"bamarni/composer-bin-plugin": true, "composer/package-versions-deprecated": true}, "sort-packages": true}}